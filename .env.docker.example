# RedAI MCP Servers Docker Environment Configuration
# Copy this file to .env and update with your actual values for Docker deployment

# =============================================================================
# GLOBAL CONFIGURATION
# =============================================================================
REDAI_API_BASE_URL=https://api.redai.com

# =============================================================================
# AFFILIATE SERVER CONFIGURATION
# =============================================================================
REDAI_AFFILIATE_API_BASE_URL=https://api.redai.com
AFFILIATE_HTTP_HOST=0.0.0.0
AFFILIATE_HTTP_PORT=8004
AFFILIATE_HTTP_PATH=/mcp
AFFILIATE_TRANSPORT=streamable-http

# =============================================================================
# BLOG SERVER CONFIGURATION
# =============================================================================
REDAI_BLOG_API_BASE_URL=https://api.redai.com
BLOG_HTTP_HOST=0.0.0.0
BLOG_HTTP_PORT=8005
BLOG_HTTP_PATH=/mcp
BLOG_TRANSPORT=streamable-http

# =============================================================================
# MODEL SERVER CONFIGURATION
# =============================================================================
REDAI_MODEL_API_BASE_URL=https://api.redai.com
MODEL_HTTP_HOST=0.0.0.0
MODEL_HTTP_PORT=8006
MODEL_HTTP_PATH=/mcp
MODEL_TRANSPORT=streamable-http

# =============================================================================
# DATA SERVER CONFIGURATION
# =============================================================================
REDAI_DATA_API_BASE_URL=https://api.redai.com
DATA_HTTP_HOST=0.0.0.0
DATA_HTTP_PORT=8007
DATA_HTTP_PATH=/mcp
DATA_TRANSPORT=streamable-http

# =============================================================================
# MARKETPLACE SERVER CONFIGURATION
# =============================================================================
REDAI_MARKETPLACE_API_BASE_URL=https://api.redai.com
MARKETPLACE_HTTP_HOST=0.0.0.0
MARKETPLACE_HTTP_PORT=8008
MARKETPLACE_HTTP_PATH=/mcp
MARKETPLACE_TRANSPORT=streamable-http

# =============================================================================
# SHIPMENT SERVER CONFIGURATION
# =============================================================================
REDAI_SHIPMENT_API_BASE_URL=https://api.redai.com
SHIPMENT_HTTP_HOST=0.0.0.0
SHIPMENT_HTTP_PORT=8009
SHIPMENT_HTTP_PATH=/mcp
SHIPMENT_TRANSPORT=streamable-http

# =============================================================================
# SHIPMENT API KEYS
# =============================================================================
# GHN (Giao Hàng Nhanh) API Configuration
GHN_TOKEN=your_ghn_token_here
GHN_SHOP_ID=your_ghn_shop_id_here

# GHTK (Giao Hàng Tiết Kiệm) API Configuration
GHTK_TOKEN=your_ghtk_token_here
GHTK_PARTNER_CODE=your_ghtk_partner_code_here

# J&T Express API Configuration
JT_USERNAME=your_jt_username_here
JT_API_KEY=your_jt_api_key_here
JT_CUSTOMER_CODE=your_jt_customer_code_here

# AhaMove API Configuration
AHAMOVE_API_KEY=your_ahamove_api_key_here
AHAMOVE_TOKEN=your_ahamove_token_here
AHAMOVE_MOBILE=your_ahamove_mobile_here

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Set to 'true' in production
SECURE_HEADERS=false
CORS_ORIGINS=*

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=false
