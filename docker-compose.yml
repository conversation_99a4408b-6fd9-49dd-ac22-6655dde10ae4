version: '3.8'

services:
  # RedAI Affiliate MCP Server
  redai-affiliate:
    build:
      context: .
      dockerfile: src/server/redai_system/affiliatte/Dockerfile
    container_name: redai-affiliate-server
    ports:
      - "8004:8004"
    environment:
      - REDAI_AFFILIATE_API_BASE_URL=https://api.redai.com
      - AFFILIATE_HTTP_HOST=0.0.0.0
      - AFFILIATE_HTTP_PORT=8004
      - AFFILIATE_HTTP_PATH=/mcp
      - AFFILIATE_TRANSPORT=streamable-http
    volumes:
      - ./logs/affiliate:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Blog MCP Server
  redai-blog:
    build:
      context: .
      dockerfile: src/server/redai_system/blog/Dockerfile
    container_name: redai-blog-server
    ports:
      - "8005:8005"
    environment:
      - REDAI_BLOG_API_BASE_URL=https://api.redai.com
      - BLOG_HTTP_HOST=0.0.0.0
      - BLOG_HTTP_PORT=8005
      - BLOG_HTTP_PATH=/mcp
      - BLOG_TRANSPORT=streamable-http
    volumes:
      - ./logs/blog:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Model MCP Server
  redai-model:
    build:
      context: .
      dockerfile: src/server/redai_system/model/Dockerfile
    container_name: redai-model-server
    ports:
      - "8006:8006"
    environment:
      - REDAI_MODEL_API_BASE_URL=https://api.redai.com
      - MODEL_HTTP_HOST=0.0.0.0
      - MODEL_HTTP_PORT=8006
      - MODEL_HTTP_PATH=/mcp
      - MODEL_TRANSPORT=streamable-http
    volumes:
      - ./logs/model:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Data MCP Server
  redai-data:
    build:
      context: .
      dockerfile: src/server/redai_system/data/Dockerfile
    container_name: redai-data-server
    ports:
      - "8007:8007"
    environment:
      - REDAI_DATA_API_BASE_URL=https://api.redai.com
      - DATA_HTTP_HOST=0.0.0.0
      - DATA_HTTP_PORT=8007
      - DATA_HTTP_PATH=/mcp
      - DATA_TRANSPORT=streamable-http
    volumes:
      - ./logs/data:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8007/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Marketplace MCP Server
  redai-marketplace:
    build:
      context: .
      dockerfile: src/server/redai_system/marketplace/Dockerfile
    container_name: redai-marketplace-server
    ports:
      - "8008:8008"
    environment:
      - REDAI_MARKETPLACE_API_BASE_URL=https://api.redai.com
      - MARKETPLACE_HTTP_HOST=0.0.0.0
      - MARKETPLACE_HTTP_PORT=8008
      - MARKETPLACE_HTTP_PATH=/mcp
      - MARKETPLACE_TRANSPORT=streamable-http
    volumes:
      - ./logs/marketplace:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Shipment MCP Server
  redai-shipment:
    build:
      context: .
      dockerfile: src/server/redai_system/shipment/Dockerfile
    container_name: redai-shipment-server
    ports:
      - "8009:8009"
    environment:
      - REDAI_SHIPMENT_API_BASE_URL=https://api.redai.com
      - SHIPMENT_HTTP_HOST=0.0.0.0
      - SHIPMENT_HTTP_PORT=8009
      - SHIPMENT_HTTP_PATH=/mcp
      - SHIPMENT_TRANSPORT=streamable-http
      # Shipment API Keys (set these in .env file)
      - GHN_TOKEN=${GHN_TOKEN:-test}
      - GHN_SHOP_ID=${GHN_SHOP_ID:-test}
      - GHTK_TOKEN=${GHTK_TOKEN:-test}
      - GHTK_PARTNER_CODE=${GHTK_PARTNER_CODE:-test}
      - JT_USERNAME=${JT_USERNAME:-test}
      - JT_API_KEY=${JT_API_KEY:-test}
      - JT_CUSTOMER_CODE=${JT_CUSTOMER_CODE:-test}
      - AHAMOVE_API_KEY=${AHAMOVE_API_KEY:-test}
      - AHAMOVE_TOKEN=${AHAMOVE_TOKEN:-test}
      - AHAMOVE_MOBILE=${AHAMOVE_MOBILE:-test}
    volumes:
      - ./logs/shipment:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8009/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  redai-network:
    driver: bridge
    name: redai-mcp-network

volumes:
  logs:
    driver: local
