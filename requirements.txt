# RedAI MCP Servers Dependencies
# Core MCP and FastMCP dependencies
fastmcp>=2.3.0
mcp>=1.0.0

# HTTP client and server dependencies
httpx>=0.25.0
uvicorn>=0.24.0
fastapi>=0.104.0

# JSON and data processing
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Async support
asyncio-mqtt>=0.13.0
aiofiles>=23.2.1

# Logging and monitoring
structlog>=23.2.0
python-json-logger>=2.0.7

# Environment and configuration
python-dotenv>=1.0.0

# Development and testing (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-httpx>=0.26.0

# Security
cryptography>=41.0.0

# Additional utilities
click>=8.1.0
rich>=13.7.0
