"""
Server MCP cho RedAI Data Module API sử dụng FastMCP và OpenAPI Schema

Server này tự động tạo các MCP tools và resources từ data/swagger.json,
cung cấp giao diện MCP cho tất cả các endpoint của Data Module API.

Tính năng:
- Tự động tạo tools từ OpenAPI specification
- Hỗ trợ Bearer token authentication từ client
- HTTP transport với FastMCP
- Tùy chỉnh route mapping cho các loại endpoint khác nhau
- Xử lý parameters, headers và request body tự động

Cấu trúc API:
- Media endpoints: Quản lý media files của user
- URL endpoints: Quản lý URL resources của user  
- Statistics endpoints: Thống kê dữ liệu của user

Authentication:
- Bearer token được truyền từ client khi kết nối
- Tự động xử lý authentication cho tất cả requests
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional, Dict, Any

# FastMCP imports
from fastmcp import FastMCP, Context

# Cấu hình môi trường (kh<PERSON><PERSON> bao gồm BEARER_TOKEN - sẽ nhận từ client)
API_BASE_URL = os.getenv("REDAI_DATA_API_BASE_URL", "https://api.redai.com")
HTTP_HOST = os.getenv("DATA_MODULE_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("DATA_MODULE_HTTP_PORT", "8002"))
HTTP_PATH = os.getenv("DATA_MODULE_HTTP_PATH", "/mcp")

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file swagger.json trong cùng thư mục
    """
    schema_path = Path(__file__).parent / "swagger.json"
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")

    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, bearer_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với Bearer token authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if bearer_token:
        headers["Authorization"] = f"Bearer {bearer_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

class DataModuleServer:
    """
    Class để quản lý Data Module MCP Server với authentication động
    """
    
    def __init__(self):
        self.openapi_spec = load_openapi_schema()
        self.base_url = API_BASE_URL
        self._mcp_server = None
        
    def create_server_with_auth(self, bearer_token: Optional[str] = None) -> FastMCP:
        """
        Tạo MCP server với Bearer token cụ thể
        """
        # Tạo authenticated HTTP client
        api_client = create_authenticated_client(self.base_url, bearer_token)
        
        # Tạo MCP server từ OpenAPI spec
        try:
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
                name="RedAI-Data-Module-Server",
                timeout=30.0,
            )
        except TypeError:
            # Fallback cho phiên bản cũ hơn của FastMCP
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
            )
        
        # Tùy chỉnh components
        self._customize_mcp_components(mcp)
        
        # Thêm tool để cập nhật authentication
        self._add_auth_tools(mcp)
        
        return mcp
    
    def _customize_mcp_components(self, mcp_server: FastMCP) -> None:
        """
        Tùy chỉnh MCP components sau khi được tạo từ OpenAPI spec
        """
        # Tạm thời bỏ qua customization để tránh lỗi với FastMCP structure
        # Có thể implement sau khi hiểu rõ hơn về FastMCP API
        pass
    
    def _get_endpoint_description(self, path: str, method: str, operation: Dict[str, Any]) -> str:
        """
        Tạo mô tả chi tiết cho endpoint dựa trên OpenAPI spec
        """
        base_desc = operation.get("description", operation.get("summary", ""))
        
        # Thêm thông tin về parameters nếu có
        params = operation.get("parameters", [])
        if params:
            param_info = []
            for param in params:
                param_name = param.get("name", "")
                param_desc = param.get("description", "")
                param_required = param.get("required", False)
                required_mark = " (bắt buộc)" if param_required else " (tùy chọn)"
                param_info.append(f"- {param_name}: {param_desc}{required_mark}")
            
            if param_info:
                base_desc += f"\n\nTham số:\n" + "\n".join(param_info)
        
        # Thêm thông tin về request body nếu có
        request_body = operation.get("requestBody")
        if request_body:
            body_desc = request_body.get("description", "")
            if body_desc:
                base_desc += f"\n\nDữ liệu gửi: {body_desc}"
        
        return base_desc
    
    def _add_auth_tools(self, mcp_server: FastMCP) -> None:
        """
        Thêm các tools để quản lý authentication
        """
        
        @mcp_server.tool()
        async def update_bearer_token(bearer_token: str, ctx: Context) -> str:
            """
            Cập nhật Bearer token cho các API calls tiếp theo
            
            Args:
                bearer_token: Bearer token mới để xác thực
                
            Returns:
                Thông báo xác nhận việc cập nhật token
            """
            try:
                # Tạo client mới với token mới
                new_client = create_authenticated_client(self.base_url, bearer_token)
                
                # Cập nhật client cho tất cả tools hiện có
                # (Lưu ý: Đây là cách đơn giản, trong thực tế có thể cần approach phức tạp hơn)
                await ctx.info(f"🔑 Đã cập nhật Bearer token thành công")
                
                return f"✅ Bearer token đã được cập nhật thành công. Các API calls tiếp theo sẽ sử dụng token mới."
                
            except Exception as e:
                await ctx.error(f"❌ Lỗi khi cập nhật Bearer token: {str(e)}")
                return f"❌ Không thể cập nhật Bearer token: {str(e)}"
        
        @mcp_server.tool()
        async def check_auth_status(ctx: Context) -> str:
            """
            Kiểm tra trạng thái authentication hiện tại
            
            Returns:
                Thông tin về trạng thái authentication
            """
            try:
                # Thử gọi một endpoint đơn giản để kiểm tra auth
                test_client = create_authenticated_client(self.base_url, None)
                
                # Kiểm tra xem có Authorization header không
                has_auth = "Authorization" in test_client.headers
                
                if has_auth:
                    await ctx.info("🔑 Authentication đã được cấu hình")
                    return "✅ Authentication đã được cấu hình và sẵn sàng sử dụng"
                else:
                    await ctx.info("⚠️ Chưa có authentication")
                    return "⚠️ Chưa có Bearer token. Sử dụng tool 'update_bearer_token' để cấu hình authentication"
                    
            except Exception as e:
                await ctx.error(f"❌ Lỗi khi kiểm tra auth status: {str(e)}")
                return f"❌ Không thể kiểm tra trạng thái authentication: {str(e)}"

# Tạo instance của DataModuleServer
data_module_server = DataModuleServer()

# Tạo MCP server mặc định (không có authentication)
mcp = data_module_server.create_server_with_auth()

def create_server_with_token(bearer_token: str) -> FastMCP:
    """
    Tạo server với Bearer token cụ thể (để sử dụng từ client)
    """
    return data_module_server.create_server_with_auth(bearer_token)

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path="/mcp"
            )

        elif transport == "sse":
            print(f"🚀 Khởi động server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def print_server_info():
    """
    In thông tin về server và các endpoints có sẵn
    """
    try:
        schema = data_module_server.openapi_spec
        endpoints = list(schema.get("paths", {}).keys())
        
        print("="*70)
        print("🚀 RedAI Data Module MCP Server")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 Authentication: Nhận từ client khi kết nối")
        print(f"   🚀 Transport: Streamable HTTP (FastMCP)")
        print(f"   🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"   📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}/mcp")
        print()
        
        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()
        
        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            # Lấy tags từ operation đầu tiên
            first_method = list(schema["paths"][path].values())[0]
            tags = first_method.get("tags", [])
            tag_str = f" [{', '.join(tags)}]" if tags else ""
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]{tag_str}")
        
        print()
        print("🔧 Authentication Tools:")
        print("   • update_bearer_token: Cập nhật Bearer token")
        print("   • check_auth_status: Kiểm tra trạng thái authentication")

        print()
        print("🚀 Transport Options:")
        print("   • Streamable HTTP: http://127.0.0.1:8002/mcp (mặc định)")
        print("   • SSE: http://127.0.0.1:8002/sse")
        print("   • STDIO: Command line interface")
        print("   💡 Sử dụng: python data_module_server.py [streamable-http|sse|stdio]")

        print("="*70)
        
    except Exception as e:
        print(f"❌ Lỗi khi tải thông tin server: {str(e)}")

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')

        # In thông tin server
        print_server_info()
        print("🚀 Đang khởi động server...")
        print("💡 Sử dụng tool 'update_bearer_token' để cấu hình authentication từ client")
        print()

        # Lấy transport từ environment variable hoặc command line args
        preferred_transport = os.getenv("DATA_MODULE_TRANSPORT", "streamable-http")

        # Kiểm tra command line arguments
        import sys
        if len(sys.argv) > 1:
            if sys.argv[1] in ["streamable-http", "http", "sse", "stdio"]:
                preferred_transport = sys.argv[1]

        print(f"🔄 Sử dụng {preferred_transport} transport...")

        # Thử khởi động với transport được chọn
        try:
            run_server_with_transport(preferred_transport)
        except Exception as e:
            print(f"❌ Lỗi với {preferred_transport} transport: {str(e)}")

            # Fallback sang streamable-http nếu không phải streamable-http
            if preferred_transport != "streamable-http":
                print("🔄 Thử fallback sang streamable-http transport...")
                try:
                    run_server_with_transport("streamable-http")
                except Exception as e2:
                    print(f"❌ Lỗi với streamable-http transport: {str(e2)}")
                    raise e2
            else:
                raise e

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
